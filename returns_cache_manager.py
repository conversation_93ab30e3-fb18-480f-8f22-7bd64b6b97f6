"""
Returns Cache Manager for Matrix QP
Provides intelligent caching for log returns, cumulative returns, and normalized returns
to avoid expensive recalculations on every dashboard update.
"""

import pandas as pd
import numpy as np
import logging
import hashlib
import pickle
from typing import Dict, Optional, Tuple, Any
from datetime import datetime, timed<PERSON><PERSON>
from pathlib import Path
import threading
from dataclasses import dataclass

from config import CURRENCY_PAIRS

# Configure logging
logger = logging.getLogger(__name__)

@dataclass
class CacheEntry:
    """Represents a cached calculation result"""
    data: Any
    timestamp: datetime
    data_hash: str
    expiry_time: datetime
    
    def is_expired(self) -> bool:
        """Check if cache entry has expired"""
        return datetime.now() > self.expiry_time
    
    def is_valid_for_data(self, data_hash: str) -> bool:
        """Check if cache entry is valid for given data hash"""
        return self.data_hash == data_hash and not self.is_expired()


class ReturnsCacheManager:
    """
    Intelligent cache manager for returns calculations
    Provides multi-level caching with automatic invalidation
    """
    
    def __init__(self, cache_dir: str = "cache", max_memory_entries: int = 100):
        """
        Initialize the cache manager
        
        Args:
            cache_dir: Directory for persistent cache files
            max_memory_entries: Maximum number of entries to keep in memory
        """
        self.cache_dir = Path(cache_dir)
        self.cache_dir.mkdir(exist_ok=True)
        self.max_memory_entries = max_memory_entries
        
        # In-memory caches for different calculation types
        self._log_returns_cache: Dict[str, CacheEntry] = {}
        self._cumulative_returns_cache: Dict[str, CacheEntry] = {}
        self._realized_volatility_cache: Dict[str, CacheEntry] = {}
        self._normalized_returns_cache: Dict[str, CacheEntry] = {}
        
        # Thread lock for cache operations
        self._lock = threading.Lock()
        
        # Cache configuration - can be adjusted based on needs
        self.cache_ttl = {
            'log_returns': timedelta(minutes=5),      # 5 minutes for log returns
            'cumulative_returns': timedelta(minutes=3), # 3 minutes for cumulative
            'realized_volatility': timedelta(minutes=10), # 10 minutes for volatility
            'normalized_returns': timedelta(minutes=2)   # 2 minutes for normalized
        }

        # For high-frequency updates (like dashboard), use shorter TTL
        if max_memory_entries > 50:  # Assume high-frequency usage
            self.cache_ttl = {
                'log_returns': timedelta(minutes=2),
                'cumulative_returns': timedelta(minutes=1),
                'realized_volatility': timedelta(minutes=5),
                'normalized_returns': timedelta(seconds=30)
            }
        
        logger.info(f"Initialized ReturnsCacheManager with cache dir: {self.cache_dir}")
    
    def _generate_data_hash(self, market_data: Dict[str, pd.DataFrame]) -> str:
        """
        Generate a hash for market data to detect changes
        
        Args:
            market_data: Dictionary of market data DataFrames
            
        Returns:
            Hash string representing the data state
        """
        try:
            # Create a signature based on data shape, latest timestamps, and latest values
            signature_parts = []
            
            for pair in sorted(market_data.keys()):
                df = market_data[pair]
                if not df.empty:
                    # Include shape, latest timestamp, and latest close price
                    signature_parts.extend([
                        f"{pair}:{len(df)}",
                        f"{df.index[-1].isoformat()}",
                        f"{df['close'].iloc[-1]:.6f}" if 'close' in df.columns else "0"
                    ])
            
            signature = "|".join(signature_parts)
            return hashlib.md5(signature.encode()).hexdigest()
            
        except Exception as e:
            logger.warning(f"Error generating data hash: {e}")
            # Fallback to timestamp-based hash
            return hashlib.md5(str(datetime.now().timestamp()).encode()).hexdigest()
    
    def _cleanup_memory_cache(self, cache_dict: Dict[str, CacheEntry]):
        """Remove expired entries and enforce size limits"""
        with self._lock:
            # Remove expired entries
            expired_keys = [k for k, v in cache_dict.items() if v.is_expired()]
            for key in expired_keys:
                del cache_dict[key]
            
            # Enforce size limit (remove oldest entries)
            if len(cache_dict) > self.max_memory_entries:
                sorted_items = sorted(cache_dict.items(), key=lambda x: x[1].timestamp)
                excess_count = len(cache_dict) - self.max_memory_entries
                for key, _ in sorted_items[:excess_count]:
                    del cache_dict[key]
    
    def get_log_returns(self, market_data: Dict[str, pd.DataFrame], 
                       calculator_func) -> pd.DataFrame:
        """
        Get cached log returns or calculate if not available/expired
        
        Args:
            market_data: Market data dictionary
            calculator_func: Function to calculate log returns if cache miss
            
        Returns:
            DataFrame with log returns
        """
        data_hash = self._generate_data_hash(market_data)
        cache_key = f"log_returns_{data_hash}"
        
        # Check memory cache first
        if cache_key in self._log_returns_cache:
            entry = self._log_returns_cache[cache_key]
            if entry.is_valid_for_data(data_hash):
                logger.debug(f"Cache HIT for log returns (age: {datetime.now() - entry.timestamp})")
                return entry.data
        
        # Cache miss - calculate new values
        logger.debug("Cache MISS for log returns - calculating...")
        start_time = datetime.now()
        
        log_returns = calculator_func(market_data)
        
        calc_time = datetime.now() - start_time
        logger.info(f"Log returns calculated in {calc_time.total_seconds():.2f}s")
        
        # Store in cache
        with self._lock:
            self._log_returns_cache[cache_key] = CacheEntry(
                data=log_returns.copy(),
                timestamp=datetime.now(),
                data_hash=data_hash,
                expiry_time=datetime.now() + self.cache_ttl['log_returns']
            )
        
        # Cleanup old entries
        self._cleanup_memory_cache(self._log_returns_cache)
        
        return log_returns
    
    def get_cumulative_returns(self, market_data: Dict[str, pd.DataFrame],
                              calculator_func) -> pd.DataFrame:
        """Get cached cumulative returns or calculate if not available"""
        data_hash = self._generate_data_hash(market_data)
        cache_key = f"cumulative_returns_{data_hash}"
        
        if cache_key in self._cumulative_returns_cache:
            entry = self._cumulative_returns_cache[cache_key]
            if entry.is_valid_for_data(data_hash):
                logger.debug(f"Cache HIT for cumulative returns")
                return entry.data
        
        logger.debug("Cache MISS for cumulative returns - calculating...")
        start_time = datetime.now()
        
        cumulative_returns = calculator_func(market_data)
        
        calc_time = datetime.now() - start_time
        logger.info(f"Cumulative returns calculated in {calc_time.total_seconds():.2f}s")
        
        with self._lock:
            self._cumulative_returns_cache[cache_key] = CacheEntry(
                data=cumulative_returns.copy(),
                timestamp=datetime.now(),
                data_hash=data_hash,
                expiry_time=datetime.now() + self.cache_ttl['cumulative_returns']
            )
        
        self._cleanup_memory_cache(self._cumulative_returns_cache)
        return cumulative_returns
    
    def get_realized_volatility(self, market_data: Dict[str, pd.DataFrame],
                               calculator_func) -> pd.Series:
        """Get cached realized volatility or calculate if not available"""
        data_hash = self._generate_data_hash(market_data)
        cache_key = f"realized_volatility_{data_hash}"
        
        if cache_key in self._realized_volatility_cache:
            entry = self._realized_volatility_cache[cache_key]
            if entry.is_valid_for_data(data_hash):
                logger.debug(f"Cache HIT for realized volatility")
                return entry.data
        
        logger.debug("Cache MISS for realized volatility - calculating...")
        start_time = datetime.now()
        
        realized_volatility = calculator_func(market_data)
        
        calc_time = datetime.now() - start_time
        logger.info(f"Realized volatility calculated in {calc_time.total_seconds():.2f}s")
        
        with self._lock:
            self._realized_volatility_cache[cache_key] = CacheEntry(
                data=realized_volatility.copy(),
                timestamp=datetime.now(),
                data_hash=data_hash,
                expiry_time=datetime.now() + self.cache_ttl['realized_volatility']
            )
        
        self._cleanup_memory_cache(self._realized_volatility_cache)
        return realized_volatility
    
    def get_normalized_returns(self, cumulative_returns: pd.DataFrame,
                              realized_volatility: pd.Series,
                              calculator_func) -> pd.DataFrame:
        """Get cached normalized returns or calculate if not available"""
        # Create hash from both inputs
        cum_hash = hashlib.md5(str(cumulative_returns.values.tobytes()).encode()).hexdigest()[:8]
        vol_hash = hashlib.md5(str(realized_volatility.values.tobytes()).encode()).hexdigest()[:8]
        cache_key = f"normalized_returns_{cum_hash}_{vol_hash}"
        
        if cache_key in self._normalized_returns_cache:
            entry = self._normalized_returns_cache[cache_key]
            if not entry.is_expired():
                logger.debug(f"Cache HIT for normalized returns")
                return entry.data
        
        logger.debug("Cache MISS for normalized returns - calculating...")
        start_time = datetime.now()
        
        normalized_returns = calculator_func(cumulative_returns, realized_volatility)
        
        calc_time = datetime.now() - start_time
        logger.info(f"Normalized returns calculated in {calc_time.total_seconds():.2f}s")
        
        with self._lock:
            self._normalized_returns_cache[cache_key] = CacheEntry(
                data=normalized_returns.copy(),
                timestamp=datetime.now(),
                data_hash=f"{cum_hash}_{vol_hash}",
                expiry_time=datetime.now() + self.cache_ttl['normalized_returns']
            )
        
        self._cleanup_memory_cache(self._normalized_returns_cache)
        return normalized_returns
    
    def clear_cache(self, cache_type: Optional[str] = None):
        """
        Clear cache entries
        
        Args:
            cache_type: Specific cache to clear ('log_returns', 'cumulative_returns', 
                       'realized_volatility', 'normalized_returns') or None for all
        """
        with self._lock:
            if cache_type is None:
                self._log_returns_cache.clear()
                self._cumulative_returns_cache.clear()
                self._realized_volatility_cache.clear()
                self._normalized_returns_cache.clear()
                logger.info("Cleared all caches")
            elif cache_type == 'log_returns':
                self._log_returns_cache.clear()
                logger.info("Cleared log returns cache")
            elif cache_type == 'cumulative_returns':
                self._cumulative_returns_cache.clear()
                logger.info("Cleared cumulative returns cache")
            elif cache_type == 'realized_volatility':
                self._realized_volatility_cache.clear()
                logger.info("Cleared realized volatility cache")
            elif cache_type == 'normalized_returns':
                self._normalized_returns_cache.clear()
                logger.info("Cleared normalized returns cache")
    
    def get_cache_stats(self) -> Dict[str, Dict[str, Any]]:
        """Get cache statistics for monitoring"""
        with self._lock:
            stats = {}
            
            for cache_name, cache_dict in [
                ('log_returns', self._log_returns_cache),
                ('cumulative_returns', self._cumulative_returns_cache),
                ('realized_volatility', self._realized_volatility_cache),
                ('normalized_returns', self._normalized_returns_cache)
            ]:
                total_entries = len(cache_dict)
                expired_entries = sum(1 for entry in cache_dict.values() if entry.is_expired())
                valid_entries = total_entries - expired_entries
                
                if cache_dict:
                    oldest_entry = min(cache_dict.values(), key=lambda x: x.timestamp)
                    newest_entry = max(cache_dict.values(), key=lambda x: x.timestamp)
                    avg_age = sum((datetime.now() - entry.timestamp).total_seconds() 
                                for entry in cache_dict.values()) / total_entries
                else:
                    oldest_entry = newest_entry = None
                    avg_age = 0
                
                stats[cache_name] = {
                    'total_entries': total_entries,
                    'valid_entries': valid_entries,
                    'expired_entries': expired_entries,
                    'oldest_entry_age': (datetime.now() - oldest_entry.timestamp).total_seconds() if oldest_entry else 0,
                    'newest_entry_age': (datetime.now() - newest_entry.timestamp).total_seconds() if newest_entry else 0,
                    'average_age_seconds': avg_age
                }
            
            return stats


# Global cache manager instance
_cache_manager = None

def get_cache_manager() -> ReturnsCacheManager:
    """Get the global cache manager instance"""
    global _cache_manager
    if _cache_manager is None:
        _cache_manager = ReturnsCacheManager()
    return _cache_manager
