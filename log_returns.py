"""
Log Returns Calculation Module for Matrix QP
Handles calculation of log returns with special handling for negative returns and absolute values
"""

import pandas as pd
import numpy as np
import logging
from typing import Dict, <PERSON><PERSON>, Optional
from numba import njit

from config import CURRENCY_PAIRS, MIN_DATA_POINTS
from returns_cache_manager import get_cache_manager

# Configure logging
logger = logging.getLogger(__name__)


class LogReturnsCalculator:
    """
    Calculator for log returns with enhanced functionality for portfolio optimization
    Handles absolute returns for risk assessment and negative weight indication
    """
    
    def __init__(self):
        """Initialize the log returns calculator"""
        self.last_calculation_time = None
        self.cached_returns = None
        self.cached_absolute_returns = None
        self.cache_manager = get_cache_manager()
        logger.info("LogReturnsCalculator initialized with caching enabled")
    
    def calculate_log_returns(self,
                            market_data: Dict[str, pd.DataFrame],
                            use_close_only: bool = True) -> pd.DataFrame:
        """
        Calculate log returns for all currency pairs

        Args:
            market_data: Dictionary mapping pair names to OHLC DataFrames
            use_close_only: Whether to use only close prices (default: True)

        Returns:
            DataFrame with log returns for each pair
        """
        logger.info(f"Calculating log returns for {len(market_data)} pairs")

        if not market_data:
            logger.warning("No market data provided for log returns calculation")
            return pd.DataFrame()

        # Use cache manager for log returns calculation
        return self.cache_manager.get_log_returns(
            market_data,
            lambda data: self._calculate_log_returns_uncached(data, use_close_only)
        )

    def _calculate_log_returns_uncached(self,
                                      market_data: Dict[str, pd.DataFrame],
                                      use_close_only: bool = True) -> pd.DataFrame:
        """
        Internal method to calculate log returns without caching
        """
        
        log_returns = {}
        failed_pairs = []
        
        for pair, df in market_data.items():
            try:
                if df.empty:
                    logger.warning(f"Empty DataFrame for {pair}")
                    failed_pairs.append(pair)
                    continue
                
                # Calculate log returns for the pair
                pair_returns = self._calculate_pair_log_returns(df, pair, use_close_only)
                
                if pair_returns is not None and len(pair_returns) > 0:
                    log_returns[pair] = pair_returns
                else:
                    failed_pairs.append(pair)
                    
            except Exception as e:
                logger.error(f"Error calculating log returns for {pair}: {str(e)}")
                failed_pairs.append(pair)
        
        if failed_pairs:
            logger.warning(f"Failed to calculate returns for pairs: {failed_pairs}")
        
        # Create DataFrame from returns
        if log_returns:
            returns_df = pd.DataFrame(log_returns)
            
            # Align all series to common time index
            returns_df = self._align_time_series(returns_df)
            
            # Cache the results
            self.cached_returns = returns_df.copy()
            self.last_calculation_time = pd.Timestamp.now()
            
            logger.info(f"Successfully calculated log returns: {returns_df.shape}")
            return returns_df
        else:
            logger.error("No valid log returns calculated")
            return pd.DataFrame()
    
    def _calculate_pair_log_returns(self, 
                                  df: pd.DataFrame, 
                                  pair: str,
                                  use_close_only: bool = True) -> Optional[pd.Series]:
        """
        Calculate log returns for a single currency pair
        
        Args:
            df: OHLC DataFrame for the pair
            pair: Currency pair name
            use_close_only: Whether to use only close prices
            
        Returns:
            Series with log returns or None if failed
        """
        try:
            if use_close_only:
                if 'close' not in df.columns:
                    logger.error(f"Close column not found for {pair}")
                    return None
                
                prices = df['close'].copy()
            else:
                # Use typical price (HLC/3) for more robust returns
                if not all(col in df.columns for col in ['high', 'low', 'close']):
                    logger.error(f"Required OHLC columns not found for {pair}")
                    return None
                
                prices = (df['high'] + df['low'] + df['close']) / 3
            
            # Remove any NaN or infinite values
            prices = prices.dropna()
            prices = prices[np.isfinite(prices)]
            
            if len(prices) < 2:
                logger.warning(f"Insufficient price data for {pair}: {len(prices)} points")
                return None
            
            # Calculate log returns
            log_returns = np.log(prices / prices.shift(1))
            
            # Remove the first NaN value from the shift operation
            log_returns = log_returns.dropna()
            
            # Additional cleaning
            log_returns = log_returns[np.isfinite(log_returns)]
            
            if len(log_returns) < MIN_DATA_POINTS:
                logger.warning(f"Insufficient return data for {pair}: {len(log_returns)} points")
                return None
            
            return log_returns
            
        except Exception as e:
            logger.error(f"Error in _calculate_pair_log_returns for {pair}: {str(e)}")
            return None
    
    def calculate_absolute_returns(self, 
                                 log_returns: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        Calculate absolute log returns for risk assessment
        
        Args:
            log_returns: DataFrame with log returns (uses cached if None)
            
        Returns:
            DataFrame with absolute log returns
        """
        if log_returns is None:
            if self.cached_returns is None:
                raise ValueError("No log returns available. Calculate log returns first.")
            log_returns = self.cached_returns
        
        # Calculate absolute returns
        absolute_returns = log_returns.abs()
        
        # Cache the results
        self.cached_absolute_returns = absolute_returns.copy()
        
        logger.info(f"Calculated absolute returns: {absolute_returns.shape}")
        return absolute_returns
    
    def get_return_statistics(self, 
                            log_returns: Optional[pd.DataFrame] = None) -> Dict[str, Dict[str, float]]:
        """
        Calculate comprehensive return statistics for each pair
        
        Args:
            log_returns: DataFrame with log returns (uses cached if None)
            
        Returns:
            Dictionary with statistics for each pair
        """
        if log_returns is None:
            if self.cached_returns is None:
                raise ValueError("No log returns available. Calculate log returns first.")
            log_returns = self.cached_returns
        
        statistics = {}
        
        for pair in log_returns.columns:
            returns = log_returns[pair].dropna()
            
            if len(returns) == 0:
                continue
            
            stats = {
                'mean': float(returns.mean()),
                'std': float(returns.std()),
                'skewness': float(returns.skew()),
                'kurtosis': float(returns.kurtosis()),
                'min': float(returns.min()),
                'max': float(returns.max()),
                'count': len(returns),
                'positive_ratio': float((returns > 0).sum() / len(returns)),
                'negative_ratio': float((returns < 0).sum() / len(returns)),
                'cumulative_return': float(returns.sum()),
                'volatility_annualized': float(returns.std() * np.sqrt(252 * 24 * 60))  # For 1-min data
            }
            
            # Calculate percentiles
            percentiles = [1, 5, 10, 25, 50, 75, 90, 95, 99]
            for p in percentiles:
                stats[f'percentile_{p}'] = float(returns.quantile(p / 100))
            
            statistics[pair] = stats
        
        return statistics
    
    def identify_negative_return_pairs(self, 
                                     log_returns: Optional[pd.DataFrame] = None,
                                     threshold: float = 0.0) -> Dict[str, bool]:
        """
        Identify pairs with negative cumulative returns for weight direction
        
        Args:
            log_returns: DataFrame with log returns (uses cached if None)
            threshold: Threshold for considering returns as negative
            
        Returns:
            Dictionary mapping pair names to boolean (True if negative)
        """
        if log_returns is None:
            if self.cached_returns is None:
                raise ValueError("No log returns available. Calculate log returns first.")
            log_returns = self.cached_returns
        
        negative_pairs = {}
        
        for pair in log_returns.columns:
            returns = log_returns[pair].dropna()
            
            if len(returns) == 0:
                negative_pairs[pair] = False
                continue
            
            cumulative_return = returns.sum()
            negative_pairs[pair] = cumulative_return < threshold
        
        logger.info(f"Identified {sum(negative_pairs.values())} pairs with negative returns")
        return negative_pairs
    
    def _align_time_series(self, returns_df: pd.DataFrame) -> pd.DataFrame:
        """
        Align all return series to a common time index
        
        Args:
            returns_df: DataFrame with potentially misaligned time series
            
        Returns:
            DataFrame with aligned time series
        """
        if returns_df.empty:
            return returns_df
        
        # Find the common time range
        start_time = returns_df.index.min()
        end_time = returns_df.index.max()
        
        # Create a complete time index (assuming 15-minute intervals)
        freq = pd.infer_freq(returns_df.index[:10])  # Infer frequency from first 10 points
        if freq is None:
            freq = '15T'  # Default to 15 minutes
        
        complete_index = pd.date_range(start=start_time, end=end_time, freq=freq)
        
        # Reindex all series to the complete index
        aligned_df = returns_df.reindex(complete_index)
        
        # Forward fill small gaps (up to 2 periods)
        aligned_df = aligned_df.ffill(limit=2)
        
        # Drop rows where all values are NaN
        aligned_df = aligned_df.dropna(how='all')
        
        logger.debug(f"Aligned time series: {aligned_df.shape}")
        return aligned_df
    
    def get_correlation_matrix(self, 
                             log_returns: Optional[pd.DataFrame] = None,
                             method: str = 'pearson') -> pd.DataFrame:
        """
        Calculate correlation matrix of log returns
        
        Args:
            log_returns: DataFrame with log returns (uses cached if None)
            method: Correlation method ('pearson', 'spearman', 'kendall')
            
        Returns:
            Correlation matrix DataFrame
        """
        if log_returns is None:
            if self.cached_returns is None:
                raise ValueError("No log returns available. Calculate log returns first.")
            log_returns = self.cached_returns
        
        # Calculate correlation matrix
        correlation_matrix = log_returns.corr(method=method)
        
        logger.info(f"Calculated {method} correlation matrix: {correlation_matrix.shape}")
        return correlation_matrix
    
    def validate_returns_quality(self, 
                                log_returns: Optional[pd.DataFrame] = None) -> Dict[str, bool]:
        """
        Validate the quality of calculated returns
        
        Args:
            log_returns: DataFrame with log returns (uses cached if None)
            
        Returns:
            Dictionary mapping pair names to quality status
        """
        if log_returns is None:
            if self.cached_returns is None:
                raise ValueError("No log returns available. Calculate log returns first.")
            log_returns = self.cached_returns
        
        quality_status = {}
        
        for pair in log_returns.columns:
            returns = log_returns[pair].dropna()
            
            # Quality checks
            checks = {
                'sufficient_data': len(returns) >= MIN_DATA_POINTS,
                'no_extreme_outliers': (returns.abs() < 0.1).all(),  # No single return > 10%
                'reasonable_volatility': 0.0001 < returns.std() < 0.05,  # Reasonable volatility range
                'no_constant_values': returns.nunique() > 1
            }
            
            quality_status[pair] = all(checks.values())
            
            if not quality_status[pair]:
                failed_checks = [check for check, passed in checks.items() if not passed]
                logger.warning(f"Quality issues for {pair}: {failed_checks}")
        
        return quality_status


# Numba-optimized functions for performance
@njit
def fast_log_returns(prices):
    """
    Fast calculation of log returns using Numba
    
    Args:
        prices: Array of prices
        
    Returns:
        Array of log returns
    """
    n = len(prices)
    if n < 2:
        return np.array([])
    
    returns = np.empty(n - 1)
    for i in range(1, n):
        if prices[i] > 0 and prices[i-1] > 0:
            returns[i-1] = np.log(prices[i] / prices[i-1])
        else:
            returns[i-1] = 0.0
    
    return returns


@njit
def fast_cumulative_returns(log_returns):
    """
    Fast calculation of cumulative returns using Numba
    
    Args:
        log_returns: Array of log returns
        
    Returns:
        Array of cumulative returns
    """
    n = len(log_returns)
    if n == 0:
        return np.array([])
    
    cumulative = np.empty(n)
    cumulative[0] = log_returns[0]
    
    for i in range(1, n):
        cumulative[i] = cumulative[i-1] + log_returns[i]
    
    return cumulative


# Convenience function
def calculate_returns_from_data(market_data: Dict[str, pd.DataFrame]) -> Tuple[pd.DataFrame, pd.DataFrame]:
    """
    Convenience function to calculate both regular and absolute returns
    
    Args:
        market_data: Dictionary mapping pair names to OHLC DataFrames
        
    Returns:
        Tuple of (log_returns_df, absolute_returns_df)
    """
    calculator = LogReturnsCalculator()
    
    # Calculate log returns
    log_returns = calculator.calculate_log_returns(market_data)
    
    # Calculate absolute returns
    absolute_returns = calculator.calculate_absolute_returns(log_returns)
    
    return log_returns, absolute_returns


if __name__ == "__main__":
    # Test the log returns calculator
    logging.basicConfig(level=logging.INFO)
    
    print("Testing Log Returns Calculator...")
    
    # Create sample data for testing
    dates = pd.date_range('2024-01-01', periods=100, freq='15T')
    sample_data = {}
    
    for pair in CURRENCY_PAIRS[:3]:  # Test with first 3 pairs
        # Generate sample price data with random walk
        np.random.seed(42)
        prices = 1.0 + np.cumsum(np.random.normal(0, 0.001, len(dates)))
        
        sample_data[pair] = pd.DataFrame({
            'open': prices * (1 + np.random.normal(0, 0.0001, len(dates))),
            'high': prices * (1 + np.abs(np.random.normal(0, 0.0002, len(dates)))),
            'low': prices * (1 - np.abs(np.random.normal(0, 0.0002, len(dates)))),
            'close': prices,
            'tick_volume': np.random.randint(100, 1000, len(dates))
        }, index=dates)
    
    # Test the calculator
    calculator = LogReturnsCalculator()
    
    # Calculate returns
    log_returns = calculator.calculate_log_returns(sample_data)
    print(f"✓ Log returns calculated: {log_returns.shape}")
    
    # Calculate absolute returns
    abs_returns = calculator.calculate_absolute_returns()
    print(f"✓ Absolute returns calculated: {abs_returns.shape}")
    
    # Get statistics
    stats = calculator.get_return_statistics()
    print(f"✓ Statistics calculated for {len(stats)} pairs")
    
    # Identify negative return pairs
    negative_pairs = calculator.identify_negative_return_pairs()
    print(f"✓ Negative pairs identified: {sum(negative_pairs.values())}")
    
    # Validate quality
    quality = calculator.validate_returns_quality()
    print(f"✓ Quality validation: {sum(quality.values())}/{len(quality)} pairs passed")
