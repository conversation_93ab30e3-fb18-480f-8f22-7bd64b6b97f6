#!/usr/bin/env python3
"""
Test script to verify μₜ currency signs are working correctly
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def create_test_data():
    """Create synthetic test data with clear currency trends"""
    
    # Create time index for last 2 hours
    end_time = datetime.now()
    start_time = end_time - timedelta(hours=2)
    time_index = pd.date_range(start=start_time, end=end_time, freq='1min')
    
    # Define currency pairs
    pairs = [
        'EURUSD', 'GBPUSD', 'AUDUSD', 'NZDUSD', 'USDCAD', 'USDCHF', 'USDJPY',
        'EURGBP', 'EURAUD', 'EURNZD', 'EURCAD', 'EURCHF', 'EURJPY',
        'GBPAUD', 'GBPNZD', 'GBPCAD', 'GBPCHF', 'GBPJPY',
        'AUDNZD', 'AUDCAD', 'AUDCHF', 'AUDJPY',
        'NZDCAD', 'NZDCHF', 'NZDJPY',
        'CADCHF', 'CADJPY',
        'CHFJPY'
    ]
    
    # Create base random data
    np.random.seed(42)
    base_data = np.random.randn(len(time_index), len(pairs)) * 0.001
    
    # Add clear trends for testing:
    # EUR weakening (negative μₜ expected)
    eur_pairs = [p for p in pairs if 'EUR' in p]
    for i, pair in enumerate(eur_pairs):
        col_idx = pairs.index(pair)
        if pair.startswith('EUR'):
            # EUR base currency - add downward trend
            base_data[:, col_idx] += np.linspace(0, -0.01, len(time_index))
        else:
            # EUR quote currency - add upward trend  
            base_data[:, col_idx] += np.linspace(0, 0.01, len(time_index))
    
    # GBP strengthening (positive μₜ expected)
    gbp_pairs = [p for p in pairs if 'GBP' in p]
    for i, pair in enumerate(gbp_pairs):
        col_idx = pairs.index(pair)
        if pair.startswith('GBP'):
            # GBP base currency - add upward trend
            base_data[:, col_idx] += np.linspace(0, 0.008, len(time_index))
        else:
            # GBP quote currency - add downward trend
            base_data[:, col_idx] += np.linspace(0, -0.008, len(time_index))
    
    # USD neutral (μₜ near 0 expected)
    usd_pairs = [p for p in pairs if 'USD' in p]
    for i, pair in enumerate(usd_pairs):
        col_idx = pairs.index(pair)
        # Add small random walk around 0
        base_data[:, col_idx] += np.random.randn(len(time_index)) * 0.0005
    
    # Create DataFrame
    df = pd.DataFrame(base_data, index=time_index, columns=pairs)
    
    return df

def main():
    logger.info("Testing μₜ currency signs with synthetic data...")
    
    # Create test data
    normalized_returns_df = create_test_data()
    logger.info(f"Created test data with {len(normalized_returns_df)} rows and {len(normalized_returns_df.columns)} pairs")
    
    # Test chart creation with μₜ values
    from dispersion_charts import DispersionChartCreator
    chart_creator = DispersionChartCreator()
    
    logger.info("Creating currency CSSD chart with μₜ values...")
    fig, pair_contributions, currency_weights, sharpe_optimized_allocation, currency_mu_t_latest = chart_creator.create_currency_cssd_chart(normalized_returns_df)
    
    logger.info(f"Chart created successfully with {len(fig.data)} traces")
    
    # Display μₜ values and expected signs
    logger.info("\n" + "="*60)
    logger.info("CURRENCY μₜ VALUES AND SIGNS")
    logger.info("="*60)
    
    currencies = ['USD', 'EUR', 'GBP', 'AUD', 'NZD', 'CAD', 'CHF', 'JPY']
    
    for currency in currencies:
        if currency in currency_mu_t_latest:
            mu_t_value = currency_mu_t_latest[currency]
            sign = "+" if mu_t_value >= 0 else "-"
            logger.info(f"{currency}: μₜ = {mu_t_value:.6f} → Sign: {sign}")
        else:
            logger.info(f"{currency}: No μₜ data available")
    
    # Test dashboard table creation
    logger.info("\n" + "="*60)
    logger.info("TESTING DASHBOARD TABLE CREATION")
    logger.info("="*60)
    
    from dashboard import PortfolioDashboard
    dashboard = PortfolioDashboard()
    
    # Create table with μₜ signs
    table_div = dashboard._create_pair_contributions_table(
        pair_contributions, 
        currency_weights, 
        sharpe_optimized_allocation, 
        currency_mu_t_latest
    )
    
    logger.info("✅ Dashboard table created successfully with μₜ currency signs!")
    
    # Verify expected results
    logger.info("\n" + "="*60)
    logger.info("EXPECTED RESULTS VERIFICATION")
    logger.info("="*60)
    logger.info("EUR should show negative sign (weakening trend)")
    logger.info("GBP should show positive sign (strengthening trend)")
    logger.info("USD should show sign near zero (neutral trend)")
    
    if 'EUR' in currency_mu_t_latest:
        eur_sign = "+" if currency_mu_t_latest['EUR'] >= 0 else "-"
        logger.info(f"✅ EUR: {eur_sign} (μₜ = {currency_mu_t_latest['EUR']:.6f})")
    
    if 'GBP' in currency_mu_t_latest:
        gbp_sign = "+" if currency_mu_t_latest['GBP'] >= 0 else "-"
        logger.info(f"✅ GBP: {gbp_sign} (μₜ = {currency_mu_t_latest['GBP']:.6f})")
    
    if 'USD' in currency_mu_t_latest:
        usd_sign = "+" if currency_mu_t_latest['USD'] >= 0 else "-"
        logger.info(f"✅ USD: {usd_sign} (μₜ = {currency_mu_t_latest['USD']:.6f})")

if __name__ == "__main__":
    main()
