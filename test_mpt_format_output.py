#!/usr/bin/env python3
"""
Test script to show the MPT format output that will be generated.
"""

import logging
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from dispersion_charts import DispersionChartCreator

# Set up logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_mpt_format_output():
    """Test and display the MPT format output"""
    
    logger.info("Testing MPT format output...")
    
    # Create test data
    start_time = datetime.now().replace(hour=9, minute=0, second=0, microsecond=0)
    time_index = [start_time + timedelta(minutes=i) for i in range(100)]
    
    # Create normalized returns data for all 28 currency pairs
    currency_pairs = [
        'EURUSD', 'EURGBP', 'EURAUD', 'EURNZD', 'EURCHF', 'EURCAD', 'EURJPY',
        'GBPUSD', 'GBPAUD', 'GBPNZD', 'GBPCHF', 'GBPCAD', 'GBPJPY',
        'AUDUSD', 'AUDNZD', 'AUDCHF', 'AUDCAD', 'AUDJPY',
        'NZDUSD', 'NZDCHF', 'NZDCAD', 'NZDJPY',
        'USDCHF', 'USDCAD', 'USDJPY',
        'CADCHF', 'CADJPY',
        'CHFJPY'
    ]
    
    np.random.seed(42)  # For reproducible results
    
    # Generate correlated returns to simulate realistic currency behavior
    normalized_returns_data = {}
    for pair in currency_pairs:
        # Add some correlation structure
        base_returns = np.random.normal(0, 0.001, len(time_index))
        normalized_returns_data[pair] = base_returns
    
    # Create DataFrame
    normalized_returns_df = pd.DataFrame(normalized_returns_data, index=time_index)
    
    # Test chart creation with weights
    chart_creator = DispersionChartCreator()
    
    logger.info("Creating currency CSSD chart with weights...")
    fig, pair_contributions, currency_weights, sharpe_optimized_allocation = chart_creator.create_currency_cssd_chart(normalized_returns_df)
    
    logger.info(f"Chart created successfully with {len(fig.data)} traces")
    
    # Generate MPT format strings
    logger.info("\n" + "="*80)
    logger.info("MPT ALLOCATION FORMAT (COPY-READY):")
    logger.info("="*80)
    
    currencies = ['USD', 'EUR', 'GBP', 'AUD', 'NZD', 'CAD', 'CHF', 'JPY']
    
    for currency in currencies:
        weights = currency_weights.get(currency, [])
        if not weights:
            continue

        # Handle "NA" case for currencies with top contributor > 50%
        if weights == "NA":
            mpt_string = "NA"
        else:
            # Create MPT format string in decimal format: "EURUSD:0.456,EURAUD:0.458,GBPUSD:-0.956"
            mpt_pairs = []
            for pair, weight in weights:
                mpt_pairs.append(f"{pair}:{weight:.3f}")

            mpt_string = ",".join(mpt_pairs)

        logger.info(f"{currency}: {mpt_string}")
    
    logger.info("="*80)
    logger.info("These strings can be copied directly into optimization boxes!")
    logger.info("="*80)

    # Show top contributor percentages for verification
    logger.info("\n" + "="*80)
    logger.info("TOP CONTRIBUTOR PERCENTAGES (for verification):")
    logger.info("="*80)
    for currency in ['USD', 'EUR', 'GBP', 'AUD', 'NZD', 'CAD', 'CHF', 'JPY']:
        contributions = pair_contributions.get(currency, [])
        if contributions:
            top_pair, top_contrib = contributions[0]
            total_contrib = sum([abs(c[1]) for c in contributions])
            top_pct = (abs(top_contrib) / total_contrib * 100) if total_contrib > 0 else 0
            logger.info(f"{currency}: {top_pair} = {top_pct:.1f}%")
    logger.info("="*80)

    # Show Sharpe-optimized allocation
    if sharpe_optimized_allocation:
        logger.info("\n" + "="*80)
        logger.info("SHARPE-OPTIMIZED ALLOCATION (TOP PAIRS):")
        logger.info("="*80)
        logger.info(f"TOP: {sharpe_optimized_allocation}")
        logger.info("="*80)
    else:
        logger.info("No Sharpe-optimized allocation generated")

    return True

if __name__ == "__main__":
    success = test_mpt_format_output()
    if success:
        logger.info("✅ MPT format test completed!")
    else:
        logger.error("❌ MPT format test failed!")
