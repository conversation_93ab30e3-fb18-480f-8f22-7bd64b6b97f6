"""
Test script to demonstrate the performance benefits of the new caching system
"""

import time
import logging
from datetime import datetime
from mt5_connector import MT5Connector
from log_returns import LogReturnsCalculator
from dispersion_calculator import DispersionCalculator
from returns_cache_manager import get_cache_manager

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def test_caching_performance():
    """Test the performance improvement from caching"""
    
    logger.info("🚀 Starting caching performance test...")
    
    # Initialize components
    mt5_connector = MT5Connector()
    returns_calculator = LogReturnsCalculator()
    dispersion_calculator = DispersionCalculator()
    cache_manager = get_cache_manager()
    
    try:
        # Connect to MT5
        if not mt5_connector.ensure_connection():
            logger.error("Failed to connect to MT5")
            return
        
        logger.info("✅ Connected to MT5")
        
        # Fetch market data
        logger.info("📊 Fetching market data...")
        market_data = mt5_connector.fetch_daily_data(current_day_only=True)
        
        if not market_data:
            logger.error("No market data available")
            return
        
        logger.info(f"✅ Fetched data for {len(market_data)} pairs")
        
        # Clear cache to start fresh
        cache_manager.clear_cache()
        
        # Test 1: First calculation (cache miss)
        logger.info("\n🔥 TEST 1: First calculation (cache miss)")
        start_time = time.time()
        
        # Calculate log returns
        log_returns = returns_calculator.calculate_log_returns(market_data)
        log_returns_time = time.time() - start_time
        
        # Calculate cumulative returns
        start_time = time.time()
        cumulative_returns = dispersion_calculator.calculate_cumulative_returns_since_sod(market_data)
        cumulative_time = time.time() - start_time
        
        # Calculate realized volatility
        start_time = time.time()
        realized_volatility = dispersion_calculator.calculate_realized_volatility(market_data)
        volatility_time = time.time() - start_time
        
        # Calculate normalized returns
        start_time = time.time()
        normalized_returns = dispersion_calculator.normalize_returns_by_realized_vol(
            cumulative_returns, realized_volatility
        )
        normalized_time = time.time() - start_time
        
        total_time_1 = log_returns_time + cumulative_time + volatility_time + normalized_time
        
        logger.info(f"📈 Log returns: {log_returns_time:.3f}s")
        logger.info(f"📈 Cumulative returns: {cumulative_time:.3f}s")
        logger.info(f"📈 Realized volatility: {volatility_time:.3f}s")
        logger.info(f"📈 Normalized returns: {normalized_time:.3f}s")
        logger.info(f"⏱️  Total time (cache miss): {total_time_1:.3f}s")
        
        # Test 2: Second calculation (cache hit)
        logger.info("\n🔥 TEST 2: Second calculation (cache hit)")
        start_time = time.time()
        
        # Calculate log returns (should be cached)
        log_returns_2 = returns_calculator.calculate_log_returns(market_data)
        log_returns_time_2 = time.time() - start_time
        
        # Calculate cumulative returns (should be cached)
        start_time = time.time()
        cumulative_returns_2 = dispersion_calculator.calculate_cumulative_returns_since_sod(market_data)
        cumulative_time_2 = time.time() - start_time
        
        # Calculate realized volatility (should be cached)
        start_time = time.time()
        realized_volatility_2 = dispersion_calculator.calculate_realized_volatility(market_data)
        volatility_time_2 = time.time() - start_time
        
        # Calculate normalized returns (should be cached)
        start_time = time.time()
        normalized_returns_2 = dispersion_calculator.normalize_returns_by_realized_vol(
            cumulative_returns_2, realized_volatility_2
        )
        normalized_time_2 = time.time() - start_time
        
        total_time_2 = log_returns_time_2 + cumulative_time_2 + volatility_time_2 + normalized_time_2
        
        logger.info(f"📈 Log returns: {log_returns_time_2:.3f}s")
        logger.info(f"📈 Cumulative returns: {cumulative_time_2:.3f}s")
        logger.info(f"📈 Realized volatility: {volatility_time_2:.3f}s")
        logger.info(f"📈 Normalized returns: {normalized_time_2:.3f}s")
        logger.info(f"⏱️  Total time (cache hit): {total_time_2:.3f}s")
        
        # Calculate performance improvement
        speedup = total_time_1 / total_time_2 if total_time_2 > 0 else float('inf')
        time_saved = total_time_1 - total_time_2
        
        logger.info(f"\n🎯 PERFORMANCE RESULTS:")
        logger.info(f"⚡ Speedup: {speedup:.1f}x faster")
        logger.info(f"⏰ Time saved: {time_saved:.3f}s ({time_saved/total_time_1*100:.1f}% reduction)")
        
        # Verify data integrity
        logger.info(f"\n🔍 DATA INTEGRITY CHECK:")
        log_returns_match = log_returns.equals(log_returns_2)
        cumulative_match = cumulative_returns.equals(cumulative_returns_2)
        volatility_match = realized_volatility.equals(realized_volatility_2)
        normalized_match = normalized_returns.equals(normalized_returns_2)
        
        logger.info(f"✅ Log returns match: {log_returns_match}")
        logger.info(f"✅ Cumulative returns match: {cumulative_match}")
        logger.info(f"✅ Realized volatility match: {volatility_match}")
        logger.info(f"✅ Normalized returns match: {normalized_match}")
        
        all_match = log_returns_match and cumulative_match and volatility_match and normalized_match
        logger.info(f"🎯 All data matches: {all_match}")
        
        # Test 3: Multiple rapid calculations (simulating dashboard updates)
        logger.info(f"\n🔥 TEST 3: Multiple rapid calculations (simulating dashboard)")
        num_iterations = 5
        
        start_time = time.time()
        for i in range(num_iterations):
            _ = returns_calculator.calculate_log_returns(market_data)
            _ = dispersion_calculator.calculate_cumulative_returns_since_sod(market_data)
            _ = dispersion_calculator.calculate_realized_volatility(market_data)
            _ = dispersion_calculator.normalize_returns_by_realized_vol(cumulative_returns, realized_volatility)
        
        rapid_time = time.time() - start_time
        avg_time_per_iteration = rapid_time / num_iterations
        
        logger.info(f"⏱️  {num_iterations} iterations completed in {rapid_time:.3f}s")
        logger.info(f"📊 Average time per iteration: {avg_time_per_iteration:.3f}s")
        logger.info(f"🚀 Estimated speedup vs no cache: {total_time_1/avg_time_per_iteration:.1f}x")
        
        # Cache statistics
        logger.info(f"\n📊 CACHE STATISTICS:")
        cache_stats = cache_manager.get_cache_stats()
        for cache_type, stats in cache_stats.items():
            logger.info(f"  {cache_type}:")
            logger.info(f"    Total entries: {stats['total_entries']}")
            logger.info(f"    Valid entries: {stats['valid_entries']}")
            logger.info(f"    Average age: {stats['average_age_seconds']:.1f}s")
        
        logger.info(f"\n🎉 Caching performance test completed successfully!")
        
    except Exception as e:
        logger.error(f"Error during performance test: {e}")
        raise
    finally:
        mt5_connector.disconnect()

if __name__ == "__main__":
    test_caching_performance()
